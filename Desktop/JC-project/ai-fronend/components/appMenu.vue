<template>
  <el-menu
    mode="vertical"
    router
    :default-active="currentRoute"
    class="el-menu-vertical-demo"
    background-color="#000"
    text-color="#fff"
    active-text-color="#4AD3A5"
    style="height: 100%; width: 100%;border-right:1px solid #2E3136;"
  >
    <el-menu-item index="/">
      <el-icon><HomeFilled /></el-icon>
      <template #title>首页</template>
    </el-menu-item>
    <el-menu-item index="/explore">
      <el-icon><HelpFilled /></el-icon>
      <template #title>AI创作智能体</template>
    </el-menu-item>
    <!-- <el-menu-item index="/activity">
      <el-icon><Ticket /></el-icon>
      <template #title>活动</template>
    </el-menu-item> -->
    <el-divider style="border: 1px dashed #2E3136;" content-position="center">
      Ai创作
    </el-divider>
    <el-menu-item index="/picture">
      <el-icon><List /></el-icon>
      <template #title>图片创作</template>
    </el-menu-item>
    <el-menu-item index="/video">
      <el-icon><VideoPlay /></el-icon>
      <template #title>视频创作</template>
    </el-menu-item>
    <el-menu-item index="/article">
      <el-icon><Document /></el-icon>
      <template #title>文章创作</template>
    </el-menu-item>
    <el-divider style="border: 1px dashed #2E3136;" content-position="center">
      个人中心
    </el-divider>
    <el-menu-item index="/personal">
      <el-icon><UserFilled /></el-icon>
      <template #title>个人中心</template>
    </el-menu-item>
  </el-menu>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'
import { Document } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const currentRoute = computed(() => route.path)
</script>
