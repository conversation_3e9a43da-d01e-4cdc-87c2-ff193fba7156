<template>
  <div>
    <div style="text-align: center;">
      <!-- <img
        style="width: 180px;height: 180px;opacity: 0.8;"
        src="/src/assets/images/empty.png"
        alt=""
      /> -->
      <h3 style="font-style: oblique;color: #B1B1B1;letter-spacing: 3px;;">快速生成创意内容</h3>
      <div style="display: flex;justify-content: space-around;align-items: center;margin-top: 20px;gap: 20px;">
        <div
          v-for="item in stepList"
          :key="item.id"
          style="display: flex;flex-direction: column;justify-content: center;align-items: center;"
        >
          <el-icon
            size="30px"
            color="#4AD3A5"
          >
            <component :is="item.icon" />
          </el-icon>
          <span style="margin-top: 5px;font-size: 12px;color: #B1B1B1;">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
const stepList = ref([
  { id: 1, name: '创意生成', icon: 'Document' },
  { id: 2, name: '快速配置', icon: 'SetUp' },
  { id: 3, name: '快速完成', icon: 'CircleCheck' },
  { id: 4, name: '准确生成', icon: 'CopyDocument' },
]);
</script>