import request from '@/utils/http'

// 生成小说
export function generateArticle(params: {
  model: string
  messages: any[]
  max_tokens?: number
  stream?: boolean
}) {
  const apiKey = import.meta.env.VITE_ARK_API_KEY
  console.log(apiKey)
  return request.post(
    'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    params,
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
    },
  )
}

// 获取文章详情
export function getArticleDetail(id: string) {
  return request.get(`/article/${id}`)
}

// 收藏文章
export function collectArticle(id: string) {
  return request.post(`/article/${id}/collect`)
}