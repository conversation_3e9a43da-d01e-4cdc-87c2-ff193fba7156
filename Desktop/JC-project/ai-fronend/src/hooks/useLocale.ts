import { computed, ComputedRef } from 'vue';
import { useI18n } from 'vue-i18n';
import { LocaleType } from '@/lang'; // 导入合法语言类型

// 定义 Hook 返回值类型
interface UseLocaleReturn {
  currentLocale: ComputedRef<LocaleType>; // 当前语言（响应式，类型为 LocaleType）
  changeLocale: (value: LocaleType) => void; // 切换语言方法（参数必须是合法语言）
}

export default function useLocale(): UseLocaleReturn {
  const { locale } = useI18n(); // 获取 i18n 实例的 locale（响应式）

  // 当前语言：约束类型为 LocaleType
  const currentLocale: ComputedRef<LocaleType> = computed(() => {
    return locale.value as LocaleType;
  });

  // 切换语言：参数必须是 LocaleType（非法值会报错）
  const changeLocale = (value: LocaleType): void => {
    if (locale.value === value) return; // 避免重复切换
    locale.value = value;
    localStorage.setItem('locale-lang', value); // 持久化存储
  };

  return {
    currentLocale,
    changeLocale
  };
}