<template>
  <div
    class="doc-creativity"
    :style="{ height: docHeight + 'px' }"
  >
    <div class="doc-creativity-grid">
      <div
        class="doc-creativity-card"
        v-for="item in docList"
        :key="item.title"
        :style="{ animationDelay: `${item.idx * 0.1}s` }"
        @click="handleClick(item.idx,'doc')"
      >
        <div class="card-image">
          <img
            :src="item.img || 'https://picsum.photos/id/24/400/225'"
            :alt="item.title"
            class="card-img"
            @error="handleImageError($event)"
          >
          <div class="image-overlay"></div>
        </div>

        <div class="card-content">
          <h3 class="card-title">{{ item.title }}</h3>
          <div class="card-apply">
            <i class="fas fa-user-check"></i>
            <span>{{ item.apply }}</span>
          </div>
        </div>

        <div class="card-hover-effect"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'DocCreativity',
});
import { ref, onMounted } from 'vue';
import { getScreenHeight } from '@/utils/mix-height';
import { getDocById } from '@/utils/dataLoader';
import { useRouter } from 'vue-router';
const router = useRouter();

const docHeight = ref(0);

onMounted(() => {
  const screenHeight = getScreenHeight();
  const headerHeight = 230; // 假设 header 高度为 60px，根据实际情况调整
  docHeight.value = screenHeight - headerHeight;
  console.log('屏幕高度:', screenHeight, '容器高度:', docHeight.value);
});

async function handleClick(id: number, type: string) {
  router.push({
    path: '/article',
    query: { id: String(id), type },
  });
  if( id === 12){
    router.push({
      path: '/picture',
      query: { id: String(12), type },
    });
  }
}

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement;
  img.src = 'https://picsum.photos/id/24/400/225'; // 备用图片
};

// 为每个项目添加索引，用于动画延迟
const docList = ref([
  {
    idx: 0,
    title: '创作原创剧本，引导观众购买商品',
    apply: '短视频创作者、销售人员',
    img: 'https://static5.yingsaidata.com/e9ca57e30e2f11819806864167307b9e.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 1,
    title: '制定视频大纲，规划短视频的结构和内容',
    apply: '短视频创作者、编剧',
    img: 'https://static5.yingsaidata.com/9a2508285bdb33a91542632b5889c956.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 2,
    title: '便携小红书种草文案，推广产品和品牌',
    apply: '营销人员、品牌经理、小红书博主',
    img: 'https://static5.yingsaidata.com/18a68deddad03583c07eaf5cea4fb094.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 3,
    title: '撰写小红书旅游攻略，分享旅游经验和攻略',
    apply: '营销人员、旅游博主',
    img: 'https://static5.yingsaidata.com/0f46ed13b55aa82c798cb35548572a62.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 4,
    title: '撰写公众号营销文案，提高品牌销售额',
    apply: '营销人员、公众号运营人员',
    img: 'https://static5.yingsaidata.com/9b5ab4c9ed0f76043b4e91193963d3fc.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 5,
    title: '提供公众号干货教程，帮助用户提升公众号运营技巧',
    apply: '营销人员、教育专业撰稿人、公众号运营者',
    img: 'https://static5.yingsaidata.com/b912e2b106f5145084e9a752d9a6e7fc.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 6,
    title: '对文章进行续写，内容丰富，提供更多有价值的信息',
    apply: '内容编辑、营销人员、品牌运营人员',
    img: 'https://static5.yingsaidata.com/6092773982621c46704a9607f174c47a.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 7,
    title: '自动生成演讲稿，提高演讲效率',
    apply: '企业领导、销售经理、培训师',
    img: 'https://static5.yingsaidata.com/e2710f5864fa9c095aa12401f72c3780.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 8,
    title: '寻找和记录灵感源泉、丰富的文本内容和创意',
    apply: '小说作者、创作人员、编辑',
    img: 'https://static5.yingsaidata.com/bb8065a24c8ef89078a82d9ebd424092.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 9,
    title: '撰写招聘信息，包括职位描述、工资要求、薪资福利等。',
    apply: '人力资源经理、招聘专员',
    img: 'https://static5.yingsaidata.com/8235586d2586fb3cea61d9eaf9d6dc2b.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 10,
    title: '设计vlog视频脚本，分享个人生活和经验',
    apply: '视频创作者、内容创作者',
    img: 'https://static5.yingsaidata.com/e43844ed0b3bed83a3a2924470e351fc.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 11,
    title: '创建具有高度创新性和吸引力的活动标题，确保活动主题名称。',
    apply: '活动策划、市场营销、内容创作',
    img: 'https://static5.yingsaidata.com/ac404203e4f0bba6cc9908755ed91818.png?x-image-process=image/resize,p_100',
  },
  {
    idx: 12,
    title: '营造独特的节日，开展节日营销',
    apply: '营销团队、品牌管理',
    img: 'https://static5.yingsaidata.com/66de45855366fa50ca0de5c107961e04.png?x-image-process=image/resize,p_100',
  },
]);
</script>

<style lang="scss" scoped>
.doc-creativity {
  border-radius: 10px;
  margin-top: 10px;
  max-width: 100%; /* 适当增加最大宽度以容纳4列 */
  overflow-y: auto;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.section-title {
  text-align: center;
  margin-bottom: 30px; /* 略微减少间距 */

  h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #1e293b;
    font-weight: 700;
  }

  p {
    font-size: 0.95rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
  }
}

// 核心修改：固定每行4个卡片
.doc-creativity-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 固定4列 */
  gap: 20px; /* 调整间距 */
}

.doc-creativity-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-image {
  height: 140px; /* 略微减小图片高度 */
  position: relative;
  overflow: hidden;

  .card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 60%,
      rgba(0, 0, 0, 0.2) 100%
    );
  }
}

.card-content {
  padding: 16px; /* 减少内边距 */

  .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .card-apply {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem; /* 减小字体 */
    color: #64748b;

    i {
      color: #3b82f6;
      font-size: 0.85rem;
    }
  }
}

.card-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(59, 130, 246, 0.05);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.doc-creativity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.08);

  .card-image .card-img {
    transform: scale(1.05);
  }

  .card-hover-effect {
    opacity: 1;
  }

  .card-title {
    color: #3b82f6;
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .doc-creativity-grid {
    grid-template-columns: repeat(3, 1fr); /* 大屏幕下3列 */
  }
}

@media (max-width: 768px) {
  .doc-creativity {
    padding: 20px 0px;
  }

  .section-title h2 {
    font-size: 1.5rem;
  }

  .doc-creativity-grid {
    grid-template-columns: repeat(2, 1fr); /* 平板下2列 */
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .doc-creativity-grid {
    grid-template-columns: 1fr; /* 手机下1列 */
  }
}
</style>
