<template>
    <div class="video-ai-creativity">
        <!-- <div class="section-title">
            <h2>视频AI创作工具</h2>
            <p>智能辅助视频制作全流程，提升创作效率与质量</p>
        </div> -->

        <div class="video-ai-grid">
            <div
                class="video-ai-card"
                v-for="item in videoList"
                :key="item.title"
                :style="{ animationDelay: `${item.idx * 0.1}s` }"
            >
                <div class="card-image">
                    <img
                        :src="item.img || 'https://picsum.photos/id/96/400/225'"
                        :alt="item.title"
                        class="card-img"
                        @error="handleImageError($event)"
                    >
                    <div class="image-overlay"></div>
                </div>

                <div class="card-content">
                    <h3 class="card-title">{{ item.title }}</h3>
                    <div class="card-apply">
                        <i class="fas fa-user-check"></i>
                        <span>{{ item.apply }}</span>
                    </div>
                </div>

                <div class="card-hover-effect"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'VideoAICreation',
});
import { ref } from 'vue';

// 处理图片加载错误
const handleImageError = (e: Event) => {
    const img = e.target as HTMLImageElement;
    img.src = 'https://picsum.photos/id/96/400/225'; // 视频相关备用图片
};

// 视频AI创作工具列表
const videoList = ref([
  {
    idx: 0,
    title: 'AI自动生成视频脚本，匹配场景与台词',
    apply: '短视频创作者、广告制作人员',
    img: 'https://static5.yingsaidata.com/9a2508285bdb33a91542632b5889c956.png?x-image-process=image/resize,p_100', // 预留图片位置
  },
  {
    idx: 1,
    title: '创作原创剧本，引导观众购买商品',
    apply: '短视频创作者、销售人员',
    img: '', // 预留图片位置
  },
  {
    idx: 2,
    title: 'AI一键生成字幕，支持多语言翻译',
    apply: '跨境创作者、教育内容制作',
    img: '', // 预留图片位置
  },
  {
    idx: 3,
    title: '智能视频风格转换，一键切换视觉效果',
    apply: '视频特效师、内容创作者',
    img: '', // 预留图片位置
  },
  {
    idx: 4,
    title: 'AI数字人视频生成，虚拟形象播报内容',
    apply: '企业宣传、新闻播报、教育领域',
    img: '', // 预留图片位置
  },
  {
    idx: 5,
    title: '自动生成视频缩略图，优化点击吸引力',
    apply: '视频运营者、自媒体创作者',
    img: '', // 预留图片位置
  },
  {
    idx: 6,
    title: 'AI分析视频内容，提供优化建议',
    apply: '内容创作者、视频营销人员',
    img: '', // 预留图片位置
  },
  {
    idx: 7,
    title: '文本转视频，输入文字自动生成短视频',
    apply: '营销人员、教育工作者、自媒体',
    img: '', // 预留图片位置
  },
]);
</script>

<style lang="scss" scoped>
.video-ai-creativity {
    padding: 20px 0px;
    max-width: 100%;
    margin: 0 auto;
}

.section-title {
    text-align: center;
    margin-bottom: 30px;

    h2 {
        font-size: 1.8rem;
        margin-bottom: 10px;
        color: #1e293b;
        font-weight: 700;
    }

    p {
        font-size: 0.95rem;
        color: #64748b;
        max-width: 600px;
        margin: 0 auto;
    }
}

.video-ai-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.video-ai-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-image {
    height: 140px;
    position: relative;
    overflow: hidden;

    .card-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0,0,0,0) 60%, rgba(0,0,0,0.2) 100%);
    }
}

.card-content {
    padding: 16px;

    .card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 8px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .card-apply {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.8rem;
        color: #64748b;

        i {
            color: #3b82f6;
            font-size: 0.85rem;
        }
    }
}

.card-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(59, 130, 246, 0.05);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-ai-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.08);

    .card-image .card-img {
        transform: scale(1.05);
    }

    .card-hover-effect {
        opacity: 1;
    }

    .card-title {
        color: #3b82f6;
    }
}

// 响应式调整
@media (max-width: 1200px) {
    .video-ai-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .video-ai-creativity {
        padding: 20px 15px;
    }

    .section-title h2 {
        font-size: 1.5rem;
    }

    .video-ai-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .video-ai-grid {
        grid-template-columns: 1fr;
    }
}
</style>
