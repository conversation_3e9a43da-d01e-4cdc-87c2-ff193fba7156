<template>
  <div
    class="ai-toolbox"
    style="color: #fff;"
  >
    <div class="header">
      <h3>人工智能工具箱</h3>
      <span class="subtitle">有多种AI工具，助您快速生成创意</span>
    </div>
    <div class="video-ai-tabs">
      <div class="tab-item">
        <div
          class="tab-item-content"
          :class="{ 'active': activeIndex === item.id }"
          @click="handleClick(item.id)"
          v-for="item in modeList"
          :key="item.id"
        >
          <el-icon
            size="20px"
            color="#4AD3A5"
          >
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <DocCreativity v-if="activeIndex === 1" />
    <VideoAICreation v-if="activeIndex === 3" />
  </div>
</template>
<script  lang="ts" setup>
import { ref } from 'vue';
import DocCreativity from './components/doc-creativity.vue';
import VideoAICreation from './components/video-creativity.vue';
const activeIndex = ref(1);
function handleClick(id: number) {
  activeIndex.value = id;
}
const modeList = ref([
  {
    id: 1,
    name: '文章创意',
    icon: 'Document',
  },
  {
    id: 2,
    name: '图片创意',
    icon: 'PictureRounded',
  },
  {
    id: 3,
    name: '视频创意',
    icon: 'VideoPlay',
  },
  {
    id: 4,
    name: 'AI数字人',
    icon: 'User',
  },
]);
</script>
<style lang="scss" scoped>
.ai-toolbox {
  .header {
    margin-bottom: 15px;
    padding-bottom: 10px ;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    h3 {
      font-size: 1.5rem;
      margin-bottom: 8px;
      background: linear-gradient(90deg, #4ade80, #60a5fa);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    .subtitle {
      margin-left: 10px;
      font-size: 13px;
      color: #94a3b8;
    }
  }
}
@media screen and (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
}
.video-ai-tabs {
  //   background-color: #1e293b;
  //   padding: 15px;
  margin-top: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

  .tab-item {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 5px;
    flex-wrap: wrap;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #334155;
      border-radius: 2px;
    }

    .tab-item-content {
      display: flex;
      align-items: center;
      gap: 10px;
      background-color: #334155;
      font-size: 14px;
      color: #cbd5e1;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      white-space: nowrap;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(74, 222, 128, 0.1),
          rgba(96, 165, 250, 0.1)
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover:not(.active) {
        background-color: #475569;
        color: #fff;
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }
      }
      &.active {
        background: linear-gradient(90deg, #166534, #1e40af);
        color: #fff;
        font-weight: 500;
        box-shadow: 0 0 8px rgba(74, 222, 128, 0.3);
        transform: translateY(0);

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(255, 255, 255, 0) 100%
          );
          transform: translateX(-100%);
          animation: shine 2s infinite;
        }
      }
    }
  }
}

@keyframes shine {
  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 768px) {
  .video-ai-tabs {
    padding: 10px;

    .tab-item {
      gap: 6px;
      padding: 3px;
      flex-wrap: wrap;
    }

    .tab-item-content {
      padding: 6px 10px;
      font-size: 12px;
      gap: 6px;
    }
  }
}
</style>
