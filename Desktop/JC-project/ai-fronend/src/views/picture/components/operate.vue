<template>
  <div class="image-ai">
    <el-card
      class="image-ai-item1"
      :style="{ height: docHeight + 'px' }"
    >
      <h4 style="border-bottom: 1px solid #1A2029;padding-bottom: 20px;">AI图片生成</h4>

      <!-- 加载状态 -->
      <div
        v-if="loadingData"
        class="loading-container"
      >
        <div class="loader"></div>
        <p>加载配置中...</p>
      </div>

      <!-- 动态渲染图片生成配置项 -->
      <div v-else>
        <div
          class="form-field"
          v-for="field in formData.forms"
          :key="field.name"
          :style="{order: field.sort}"
        >
          <h5>
            {{ field.name }}
            <span
              v-if="field.is_required === 1"
              class="required"
            >*</span>
          </h5>

          <div class="field-content">
            <!-- 文本描述输入（图片主题） -->
            <div v-if="field.type === 5 && field.name === '图片主题'">
              <el-input
                v-model="field.value[0]"
                style="width: 100%;"
                :placeholder="field.placeholder || `请输入${field.name}`"
                clearable
                :disabled="loading"
                type="textarea"
                autosize
              />
            </div>

            <!-- 文本输入（节日选择、补充说明等） -->
            <el-input
              v-if="field.type === 5 && field.name !== '图片主题'"
              v-model="field.value[0]"
              style="width: 100%;"
              :placeholder="field.placeholder || `请输入${field.name}`"
              clearable
              :disabled="loading"
            />

            <!-- 数字输入（如尺寸比例、数量等） -->
            <el-input
              v-if="field.type === 4"
              v-model.number="field.value[0]"
              style="width: 100%;"
              :placeholder="field.placeholder || `请输入${field.name}`"
              type="number"
              clearable
              :disabled="loading"
            />

            <!-- 单选（如风格选择） -->
            <el-radio-group
              v-if="field.type === 2"
              v-model="field.value[0]"
              :disabled="loading"
            >
              <el-radio
                v-for="option in field.options"
                :key="option"
                :label="option"
                class="radio-option"
              >
                {{ option }}
              </el-radio>
            </el-radio-group>

            <!-- 多选（改为标签输入模式，如关键词） -->
            <div
              v-if="field.type === 3"
              class="tag-group"
            >
              <el-tag
                v-for="(tag, index) in field.value"
                :key="index"
                closable
                @close="field.value.splice(index,1)"
                class="checkbox-tag"
                type="success"
                effect="light"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-model="field._input"
                size="small"
                placeholder=" + Enter 添加标签"
                @keyup.enter="if(field._input && field.value.indexOf(field._input)===-1){field.value.push(field._input);} field._input='';"
                @blur="field._input='';"
                style="width: 110px;margin-bottom: 5px;"
              />
            </div>

            <!-- 增强模式开关 -->
            <el-switch
              v-if="field.type === 11"
              v-model="field.value[0]"
              :active-value="'enabled'"
              :inactive-value="'disabled'"
              :disabled="loading"
            />

            <!-- 风格选择 - 使用卡片式选择器 -->
            <div
              v-if="field.type === 6"
              class="style-selector"
            >
              <div
                v-for="option in field.options"
                :key="option"
                class="style-card"
                :class="{ 'selected': field.value[0] === option }"
                @click="field.value[0] = option"
              >
                <div
                  class="style-preview"
                  :style="getStylePreview(option)"
                ></div>
                <div class="style-name">{{ option }}</div>
              </div>
            </div>

            <!-- 尺寸选择 - 使用卡片式选择器 -->
            <div
              v-if="field.type === 7"
              class="aspect-selector"
            >
              <div
                v-for="option in field.options"
                :key="option"
                class="aspect-card"
                :class="{ 'selected': field.value[0] === option }"
                @click="field.value[0] = option"
              >
                <div
                  class="aspect-ratio"
                  :style="{ aspectRatio: option.split(' ')[0] }"
                ></div>
                <div class="aspect-name">{{ option }}</div>
              </div>
            </div>

            <!-- 参考图上传 -->
            <el-upload
              v-if="field.type === 8"
              class="upload-demo"
              action="#"
              :show-file-list="false"
              :auto-upload="false"
              :disabled="loading"
              :on-change="(file) => { field.value[0] = file.raw }"
            >
              <el-button
                :disabled="loading"
                size="small"
                type="primary"
              >上传参考图</el-button>
              <span
                v-if="field.value[0]"
                style="margin-left: 10px;"
              >已选择: {{ field.value[0]?.name }}</span>
            </el-upload>

            <!-- 负面词 -->
            <el-input
              v-if="field.type === 9"
              v-model="field.value[0]"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              style="width: 100%;"
              :placeholder="field.placeholder || '请输入不需要的元素，多个用逗号分隔'"
              :disabled="loading"
              clearable
            />

            <!-- 布局控制 -->
            <el-select
              v-if="field.type === 10"
              v-model="field.value[0]"
              style="width: 100%;"
              :placeholder="field.placeholder || `请选择${field.name}`"
              :disabled="loading"
            >
              <el-option
                v-for="option in field.options"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>

          <!-- 字段说明 -->
          <p
            v-if="field.description"
            class="field-description"
          >{{ field.description }}</p>
          <p
            v-if="field.tips"
            class="field-tips"
          >{{ field.tips }}</p>
        </div>

        <!-- 按钮组 -->
        <div class="button-group">
          <button
            class="generate-button"
            @click="handleGenerate"
            :disabled="loading"
          >
            <span v-if="!loading">生成图片</span>
            <span
              v-else
              class="loader"
            ></span>
          </button>
          <button
            class="variation-button"
            @click="handleVariation"
            :disabled="loading || !generatedImages.length"
          >
            <span v-if="!loading">生成变体</span>
            <span
              v-else
              class="loader"
            ></span>
          </button>
        </div>
      </div>
    </el-card>
    <div
      class="image-ai-item2"
      :style="{ height: docHeight + 'px' }"
      style="padding: 20px;"
    >
      <Emptyed v-if="!generatedImages.length && !loading" />

      <!-- 图片生成加载状态 -->
      <div
        v-if="loading && !generatedImages.length"
        class="loading-container"
      >
        <div class="loader"></div>
        <p>AI正在生成图片...</p>
      </div>

      <!-- 生成的图片展示区 -->
      <div v-else-if="generatedImages.length || loading">
        <div
          class="image-stats"
          v-if="generatedImages.length"
        >
          <span>共 {{ generatedImages.length }} 张图片</span>
          <span>生成于: {{ formatGenerationTime() }}</span>
        </div>

        <div class="image-grid">
          <div
            v-for="(image, index) in generatedImages"
            :key="index"
            class="image-item"
          >
            <div class="image-wrapper">
              <img
                :src="image.url"
                :alt="`生成的图片 ${index + 1} (${image.size})`"
                class="generated-image"
                @load="onImageLoaded"
                @error="handleImageError(index)"
              >
              <div class="image-size-badge">{{ image.size }}</div>

              <!-- 图片加载失败提示 -->
              <div
                class="image-error"
                v-if="image.error"
              >
                <i class="error-icon" />
                <span>加载失败</span>
              </div>
            </div>

            <div class="image-actions">
              <el-button
                size="mini"
                icon="Download"
                @click="downloadImage(image.url, `ai-generated-${index + 1}`)"
                :disabled="image.error"
              ></el-button>
              <el-button
                size="mini"
                icon="Refresh"
                @click="regenerateImage(index)"
                :disabled="loading || image.error"
              ></el-button>
            </div>
          </div>

          <!-- 加载中的占位符 - 根据生成数量显示 -->
          <div
            v-for="i in loadingPlaceholders"
            :key="'placeholder-' + i"
            v-if="loading"
            class="image-placeholder"
          >
            <div class="image-loader"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElButton } from 'element-plus';
import Emptyed from '/Users/<USER>/Desktop/JC-project/ai-fronend/components/emptyed.vue';
import { useRoute } from 'vue-router';
import { getDocById } from '@/utils/dataLoader';
import { getScreenHeight } from '@/utils/mix-height';

// 页面高度计算
const docHeight = ref(0);
onMounted(() => {
  const screenHeight = getScreenHeight();
  const headerHeight = 180; // 头部高度
  docHeight.value = screenHeight - headerHeight;
});

defineOptions({
  name: 'ImageGenerator',
});

const route = useRoute();

// 表单数据
const formData = reactive({
  id: 0,
  forms: [],
  intention: 2, // 2表示图片生成
});

// 尺寸映射表 - 新增：将选择的比例映射为实际像素尺寸
const sizeMap: Record<string, string> = {
  '1:1': '2048x2048',
  '16:9': '2560x1440',
  '9:16': '1440x2560',
  '4:3': '2304x1728',
  '3:2': '2400x1600'
};

// 默认图片生成配置
const defaultFormConfig = {
  id: 0,
  forms: [
    {
      name: '图片主题',
      type: 5,
      sort: 1,
      description: '详细描述你想要生成的图片内容，越具体效果越好',
      tips: '例如：一片宁静的湖泊，周围环绕着雪山，清晨的阳光洒在湖面上',
      value: [''],
      is_required: 1,
      options: [],
      placeholder: '请详细描述你想要的图片内容',
    },
    {
      name: '艺术风格',
      type: 6,
      sort: 2,
      description: '选择图片的艺术风格',
      tips: '',
      value: ['现实主义'],
      is_required: 2,
      options: [
        '现实主义',
        '印象派',
        '卡通',
        '抽象',
        '超现实主义',
        '赛博朋克',
        '古风',
        '极简主义',
        '三维模型',
      ],
      placeholder: '请选择艺术风格',
    },
    {
      name: '图片尺寸',
      type: 7,
      sort: 3,
      description: '选择生成图片的尺寸比例',
      tips: '不同尺寸适合不同场景，如社交媒体、印刷等',
      value: ['16:9'],
      is_required: 2,
      options: ['1:1 正方形', '16:9 宽屏', '9:16 竖屏', '4:3 标准', '3:2 照片'],
      placeholder: '请选择图片尺寸',
    },
    {
      name: '关键词',
      type: 3,
      sort: 4,
      description: '添加额外的关键词，帮助AI更精准地生成图片',
      tips: '例如：高清、细节丰富、明亮、景深',
      value: [],
      is_required: 2,
      options: ['高清', '细节丰富', '明亮', '暗调', '景深', '光影效果'],
      placeholder: '',
    },
    {
      name: '负面词',
      type: 9,
      sort: 5,
      description: '不希望在图片中出现的元素',
      tips: '例如：模糊、低质量、水印',
      value: [''],
      is_required: 2,
      options: [],
      placeholder: '请输入不需要的元素，多个用逗号分隔',
    },
    {
      name: '参考图',
      type: 8,
      sort: 6,
      description: '上传一张参考图，AI会参考其风格或构图',
      tips: '支持JPG、PNG格式，文件大小不超过5MB',
      value: [],
      is_required: 2,
      options: [],
    },
    {
      name: '生成数量',
      type: 4,
      sort: 7,
      description: '一次生成的图片数量',
      tips: '最多同时生成4张图片',
      value: [1],
      is_required: 2,
      options: [],
      placeholder: '请输入生成数量',
    },
    {
      name: '增强模式',
      type: 11,
      sort: 100,
      description: '开启后AI将进行更精细的处理，生成质量更高但耗时更长',
      tips: '',
      value: ['disabled'],
      is_required: 2,
      options: ['enabled', 'disabled'],
    },
  ],
  intention: 2,
};

// 风格预览图的样式映射
const stylePreviews = {
  现实主义: {
    backgroundImage: 'url(https://picsum.photos/id/1015/300/200)',
    backgroundSize: 'cover',
  },
  印象派: {
    backgroundImage: 'url(https://picsum.photos/id/1019/300/200)',
    backgroundSize: 'cover',
    filter: 'saturate(1.5) hue-rotate(10deg)',
  },
  卡通: {
    backgroundImage: 'url(https://picsum.photos/id/1025/300/200)',
    backgroundSize: 'cover',
    filter: 'posterize(5) saturate(2)',
  },
  抽象: {
    backgroundImage: 'url(https://picsum.photos/id/1039/300/200)',
    backgroundSize: 'cover',
    filter: 'blur(2px) contrast(1.2)',
  },
  超现实主义: {
    backgroundImage: 'url(https://picsum.photos/id/1043/300/200)',
    backgroundSize: 'cover',
    filter: 'hue-rotate(90deg) saturate(1.3)',
  },
  赛博朋克: {
    backgroundImage: 'url(https://picsum.photos/id/1033/300/200)',
    backgroundSize: 'cover',
    filter: 'sepia(0.3) hue-rotate(180deg)',
  },
  古风: {
    backgroundImage: 'url(https://picsum.photos/id/1050/300/200)',
    backgroundSize: 'cover',
    filter: 'sepia(0.7) saturate(0.8)',
  },
  极简主义: {
    backgroundImage: 'url(https://picsum.photos/id/1060/300/200)',
    backgroundSize: 'cover',
    filter: 'contrast(1.5) saturate(0.5)',
  },
  三维模型: {
    backgroundImage: 'url(https://picsum.photos/id/1070/300/200)',
    backgroundSize: 'cover',
    filter: 'contrast(1.5) saturate(0.5)',
  },
};

// 获取风格预览样式
function getStylePreview(style: string) {
  return (
    stylePreviews[style] || {
      backgroundColor: '#2c3440',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#888',
      fontSize: '12px',
    }
  );
}

// 状态管理
const id = ref<string | null>(null);
const types = ref<string | null>('image');
const generatedImages = ref<
  Array<{
    url: string;
    timestamp: number;
    size: string;
    error?: boolean;
  }>
>([]);
const loading = ref(false);
const loadingData = ref(true);
const lastGenerationTime = ref<number | null>(null);
const imageCount = ref(1); // 记录当前要生成的图片数量

// 计算属性：根据生成数量显示相应的加载占位符
const loadingPlaceholders = computed(() => {
  return Array.from({ length: imageCount.value }, (_, i) => i + 1);
});

// 格式化生成时间
function formatGenerationTime() {
  if (!lastGenerationTime.value) return '';
  const date = new Date(lastGenerationTime.value);
  return date.toLocaleString();
}

// 处理图片加载错误
function handleImageError(index: number) {
  if (generatedImages.value[index]) {
    generatedImages.value[index].error = true;
  }
}

// 表单验证
function validateForm(formFields: any[]): boolean {
  const requiredFields = formFields.filter((field) => field.is_required === 1);

  for (const field of requiredFields) {
    if (
      !field.value ||
      field.value.length === 0 ||
      (Array.isArray(field.value) && field.value.every((v) => !v)) ||
      (typeof field.value === 'string' && !field.value.trim())
    ) {
      ElMessage.warning(`${field.name}为必填项`);
      return false;
    }
  }

  // 验证生成数量
  const countField = formFields.find((f) => f.name === '生成数量');
  if (countField && (countField.value[0] < 1 || countField.value[0] > 4)) {
    ElMessage.warning('生成数量必须在1-4之间');
    return false;
  }

  // 记录图片数量用于加载占位符
  imageCount.value = countField
    ? Math.min(4, Math.max(1, countField.value[0]))
    : 1;

  return true;
}

// 构建图片生成参数，组装API真实字段
// 修复：完善参数提取逻辑，确保所有表单值都能正确提取
function buildImageParams(formFields: any[]): any {
  const params: any = {};
  let theme = '';
  let style = '';
  let keywords: string[] = [];
  let negativePrompt = '';
  let size = '2048x2048'; // 默认尺寸
  let count = 1;
  let referenceImageFile: File | undefined = undefined;
  let enhancedMode = false;

  // 遍历所有表单字段，提取值
  formFields.forEach((field) => {
    // 确保字段值存在且有效
    if (!field.value || field.value.length === 0) return;

    const fieldValue = Array.isArray(field.value) ? field.value[0] : field.value;

    switch (field.name) {
      case '图片主题':
        theme = fieldValue;
        break;
      case '艺术风格':
        style = fieldValue;
        break;
      case '关键词':
        keywords = Array.isArray(field.value) ? field.value : [field.value];
        break;
      case '负面词':
        negativePrompt = fieldValue;
        break;
      case '图片尺寸':
        // 提取比例部分（如从"16:9 宽屏"中提取"16:9"）
        const ratio = fieldValue.split(' ')[0];
        size = sizeMap[ratio] || size;
        break;
      case '生成数量':
        count = parseInt(fieldValue, 10) || 1;
        break;
      case '参考图':
        referenceImageFile = fieldValue;
        break;
      case '增强模式':
        enhancedMode = fieldValue === 'enabled';
        break;
    }
  });

  // 组装 prompt 字段
  const promptParts: string[] = [];
  if (theme) promptParts.push(theme);
  if (style) promptParts.push(`风格: ${style}`);
  if (keywords.length > 0) promptParts.push(`关键词: ${keywords.join('，')}`);

  // 添加增强模式提示（如果开启）
  if (enhancedMode) {
    promptParts.push('使用增强模式生成高质量图片');
  }

  let prompt = promptParts.join('，');

  // 处理负面词
  if (negativePrompt && negativePrompt.trim()) {
    prompt = `${prompt}。避免: ${negativePrompt}`;
  }

  // 构建最终参数对象
  params.prompt = prompt;
  params.size = size;
  params.enhanced = enhancedMode;

  // 调试日志：输出构建的prompt
  console.log('构建的prompt参数:', params.prompt);

  // 参考图处理
  if (referenceImageFile) {
    params.reference_image = referenceImageFile;
  }

  // 多图生成配置
  if (count && count > 1) {
    params.sequential_image_generation = 'auto';
    params.sequential_image_generation_options = { max_images: count };
  }

  return params;
}

// 生成图片
async function handleGenerate() {
  if (!validateForm(formData.forms)) {
    return;
  }

  loading.value = true;
  generatedImages.value = [];

  try {
    const params = buildImageParams(formData.forms);
    let base64Image: string | undefined = undefined;

    // 处理参考图（如果有）
    if (params.reference_image && params.reference_image instanceof File) {
      base64Image = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(params.reference_image);
      });
    }

    // 构建请求体
    const requestBody: any = {
      model: 'ep-20250910162413-fjv4l',
      stream: false,
      response_format: 'url',
      watermark: true,
      prompt: params.prompt,
      size: params.size,
    };

    // 添加增强模式参数（如果API支持）
    if (params.enhanced) {
      requestBody.quality = 'enhanced';
    }

    // 处理参考图
    if (base64Image) {
      requestBody.image = base64Image;
    }

    // 处理多图生成
    if (params.sequential_image_generation) {
      requestBody.sequential_image_generation = params.sequential_image_generation;
      requestBody.sequential_image_generation_options = params.sequential_image_generation_options;
    }

    // 发送请求
    const response = await fetch('/ark-api/api/v3/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${import.meta.env.VITE_ARK_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // 处理API响应
    if (result.data && Array.isArray(result.data)) {
      const newImages = result.data.map((item: any) => ({
        url: item.url,
        timestamp: Date.now(),
        size: item.size || params.size,
      }));

      generatedImages.value = newImages;
      lastGenerationTime.value = Date.now();
      ElMessage.success(`图片生成完成，共 ${newImages.length} 张`);
    }
    else {
      // 模拟生成（当API不可用时）
      const simulatedImages = Array.from({ length: params.sequential_image_generation_options?.max_images || 1 },
        (_, i) => {
          const [width, height] = params.size.split('x').map(Number);
          return {
            url: `https://picsum.photos/seed/${Date.now() + i}/${width}/${height}`,
            timestamp: Date.now(),
            size: params.size
          };
        }
      );

      generatedImages.value = simulatedImages;
      lastGenerationTime.value = Date.now();
      ElMessage.success(`模拟生成完成，共 ${simulatedImages.length} 张`);
    }
  } catch (err) {
    console.error('生成错误：', err);
    ElMessage.error('图片生成失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 生成变体
async function handleVariation() {
  if (!generatedImages.length) {
    ElMessage.warning('请先生成图片');
    return;
  }

  loading.value = true;
  imageCount.value = generatedImages.length;

  try {
    const params = buildImageParams(formData.forms);

    // 构建变体请求体
    const requestBody = {
      model: 'ep-20250910162413-fjv4l',
      prompt: `${params.prompt}，生成上述内容的变体图片`,
      size: params.size,
      stream: false,
      response_format: 'url',
      watermark: true,
    };

    // 发送请求获取变体
    const response = await fetch('/ark-api/api/v3/images/variations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${import.meta.env.VITE_ARK_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.data && Array.isArray(result.data)) {
      const variations = result.data.map((item: any, index: number) => ({
        url: item.url,
        timestamp: Date.now(),
        size: item.size || generatedImages.value[index]?.size || params.size,
      }));

      generatedImages.value = variations;
      lastGenerationTime.value = Date.now();
      ElMessage.success('变体生成完成');
    } else {
      // 模拟生成变体
      const variations = generatedImages.value.map((img, index) => {
        const [width, height] = img.size.split('x').map(Number);
        return {
          url: `https://picsum.photos/seed/${Date.now() + index + 100}/${width}/${height}`,
          timestamp: Date.now(),
          size: img.size,
        };
      });

      generatedImages.value = variations;
      lastGenerationTime.value = Date.now();
      ElMessage.success('变体生成完成');
    }
  } catch (err) {
    console.error('变体生成错误：', err);
    ElMessage.error('变体生成失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 重新生成单张图片
async function regenerateImage(index: number) {
  if (index < 0 || index >= generatedImages.value.length) return;

  loading.value = true;
  const currentImage = generatedImages.value[index];

  try {
    const params = buildImageParams(formData.forms);
    const size = currentImage.size;

    // 构建请求体
    const requestBody = {
      model: 'ep-20250910162413-fjv4l',
      prompt: `${params.prompt}，重新生成此图片`,
      size,
      stream: false,
      response_format: 'url',
      watermark: true,
    } as any;

    if (params.enhanced) {
      requestBody.quality = 'enhanced';
    }

    // 发送请求
    const response = await fetch('/ark-api/api/v3/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${import.meta.env.VITE_ARK_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // 替换指定位置的图片
    if (result.data && Array.isArray(result.data) && result.data.length > 0) {
      generatedImages.value.splice(index, 1, {
        url: result.data[0].url,
        timestamp: Date.now(),
        size: result.data[0].size || size,
      });
    } else {
      // 模拟生成
      const [width, height] = size.split('x').map(Number);
      generatedImages.value.splice(index, 1, {
        url: `https://picsum.photos/seed/${Date.now() + index + 200}/${width}/${height}`,
        timestamp: Date.now(),
        size: size,
      });
    }

    lastGenerationTime.value = Date.now();
    ElMessage.success('图片已重新生成');
  } catch (err) {
    console.error('重新生成错误：', err);
    ElMessage.error('重新生成失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 下载图片
function downloadImage(url: string, filename: string) {
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.png`;
  document.body.appendChild(link);

  // 处理可能的跨域问题
  link.addEventListener('click', (e) => {
    e.preventDefault();
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob);
        link.href = blobUrl;
        link.click();
        URL.revokeObjectURL(blobUrl);
      })
      .catch((err) => {
        console.error('下载失败:', err);
        ElMessage.error('图片下载失败');
      });
  });

  link.click();
  document.body.removeChild(link);
}

// 图片加载完成处理
function onImageLoaded() {
  // 可以添加图片加载完成后的动画或处理
}

// 加载配置并应用路由参数
async function fetchConfig() {
  if (!id.value) {
    // 使用默认配置
    Object.assign(formData, defaultFormConfig);
    loadingData.value = false;
    // 应用路由参数
    updateFormFromQuery(route.query);
    return;
  }

  try {
    loadingData.value = true;
    const res = await getDocById(id.value);
    console.log('配置数据:', res);

    if (res && Array.isArray(res.forms)) {
      Object.assign(formData, res);
    } else {
      Object.assign(formData, defaultFormConfig);
      ElMessage.warning('配置数据格式不正确，使用默认配置');
    }

    // 应用路由参数
    updateFormFromQuery(route.query);
  } catch (error) {
    console.error('加载配置错误:', error);
    Object.assign(formData, defaultFormConfig);
    ElMessage.error('加载配置失败，使用默认配置');
    // 应用路由参数
    updateFormFromQuery(route.query);
  } finally {
    loadingData.value = false;
  }
}

// 监听路由参数变化并更新表单数据
watch(
  () => route.query,
  (newQuery) => {
    updateFormFromQuery(newQuery);
  },
  { immediate: true },
);

// 从路由参数更新表单数据
function updateFormFromQuery(query: any) {
  if (!query) return;

  formData.forms.forEach((field) => {
    // 根据字段名生成可能的参数名
    const paramNames = [
      field.name,
      field.name.replace(/\s+/g, ''),
      field.name.replace(/\s+/g, '_').toLowerCase(),
    ];

    // 查找匹配的参数
    const paramName = paramNames.find((name) => query[name] !== undefined);

    if (paramName && query[paramName] !== undefined) {
      if (field.type === 3) {
        // 标签多选
        field.value = Array.isArray(query[paramName])
          ? query[paramName]
          : query[paramName].split(',').filter(Boolean);
      } else {
        // 保持数组格式
        field.value = [query[paramName]];
      }
    }
  });
}

// 组件挂载时初始化
onMounted(() => {
  id.value = route.query.id ? String(route.query.id) : null;
  types.value = route.query.type ? String(route.query.type) : 'image';
  fetchConfig();
});
</script>

<style lang="scss" scoped>
.image-ai {
  display: grid;
  grid-template-columns: 3fr 7fr;
  gap: 20px;
  width: 100%;
  height: 100%;
  background-color: #000;
  color: #fff;
  padding: 20px;
}

@media screen and (max-width: 1210px) {
  .image-ai {
    grid-template-columns: 1fr;
  }
}

.image-ai-item1 {
  overflow-y: auto;
  scrollbar-width: none;
  padding: 20px;
  &::-webkit-scrollbar {
    display: none;
  }
}

.image-ai-item2 {
  overflow-y: auto;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.image-ai-item1,
.image-ai-item2 {
  background-color: #000;
  color: #fff;
  min-height: 600px;
  border: none;
  box-shadow: 0 0px 5px rgba(215, 213, 213, 0.6);
  transition: 0.3s all linear;
}

.image-ai-item1:hover,
.image-ai-item2:hover {
  box-shadow: 0 0px 12px rgba(215, 213, 213, 0.6);
}

.form-field {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.required {
  color: #ff6b6b;
  margin-left: 5px;
}

.field-content {
  margin-top: 10px;
}

.radio-option {
  margin-right: 15px;
  margin-bottom: 10px;
}

.textarea {
  width: 100%;
  border-radius: 8px;
  background: linear-gradient(145deg, #1b1f27, #262d37);
  padding: 12px;
  color: #e0e0e0;
  border: 1px solid transparent;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.6;
  caret-color: #4ad3a5;
  transition: all 0.3s ease;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &:focus {
    border: 1px solid #4ad3a5;
    box-shadow: 0 0 12px rgba(74, 211, 165, 0.6);
    background: linear-gradient(145deg, #20242d, #2c3440);
  }
}

.textarea::-webkit-scrollbar {
  display: none;
}

.char-count {
  text-align: right;
  font-size: 12px;
  margin-top: 6px;
  color: #888;
  &.warning {
    color: #ff6b6b;
  }
}

.button-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 20px;
}

.generate-button,
.variation-button {
  width: 100%;
  padding: 12px 20px;
  border-radius: 8px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.generate-button {
  background: linear-gradient(90deg, #4ad3a5, #2bb0ed);
  box-shadow: 0 0 10px rgba(74, 211, 165, 0.6);
  &:hover:not(:disabled) {
    background: linear-gradient(90deg, #2bb0ed, #4ad3a5);
    box-shadow: 0 0 20px rgba(74, 211, 165, 0.9);
    transform: translateY(-2px);
  }
}

.variation-button {
  background: linear-gradient(90deg, #722ed1, #eb7350);
  box-shadow: 0 0 10px rgba(114, 46, 209, 0.6);
  &:hover:not(:disabled) {
    background: linear-gradient(90deg, #eb7350, #722ed1);
    box-shadow: 0 0 20px rgba(114, 46, 209, 0.9);
    transform: translateY(-2px);
  }
}

.loader {
  display: inline-block;
  width: 17px;
  height: 17px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid #4ad3a5;
  border-right: 2px solid #2bb0ed;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.field-description {
  font-size: 12px;
  color: #888;
  margin-top: 5px;
}

.field-tips {
  font-size: 12px;
  color: #4ad3a5;
  margin-top: 5px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-container .loader {
  margin-bottom: 15px;
  width: 30px;
  height: 30px;
}

.checkbox-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

/* 图片展示区域样式优化 */
.image-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 13px;
  color: #888;
  padding: 0 5px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  width: 100%;
  padding: 10px 0;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(145deg, #1b1f27, #262d37);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  }
}

.image-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.generated-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px 8px 0 0;
  transition: opacity 0.3s ease;
  object-fit: cover;
}

/* 图片尺寸标签 */
.image-size-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  backdrop-filter: blur(2px);
}

/* 图片错误状态 */
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(30, 30, 30, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ff6b6b;
  gap: 8px;

  .error-icon {
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff6b6b'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
  }
}

.image-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.5);
  gap: 5px;
}

/* 图片加载占位符优化 */
.image-placeholder {
  aspect-ratio: 16/9;
  border-radius: 8px;
  background: linear-gradient(145deg, #1b1f27, #262d37);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.image-loader {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top: 3px solid #4ad3a5;
  border-right: 3px solid #2bb0ed;
  animation: spin 1s linear infinite;
}

/* 风格选择器样式 */
.style-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 20px;
  margin-top: 10px;
}

.style-card {
  border-radius: 8px;
  overflow: hidden;
  background: #262d37;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &.selected {
    border-color: #4ad3a5;
    box-shadow: 0 0 10px rgba(74, 211, 165, 0.6);
  }
}

.style-preview {
  width: 100%;
  height: 80px;
  position: relative;
  overflow: hidden;
}

.style-name {
  padding: 8px;
  font-size: 13px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 尺寸选择器样式 */
.aspect-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.aspect-card {
  height: 120px;
  border-radius: 8px;
  background: #262d37;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &.selected {
    border-color: #4ad3a5;
    box-shadow: 0 0 10px rgba(74, 211, 165, 0.6);
  }
}

.aspect-ratio {
  width: 100%;
  height: 70px;
  background: linear-gradient(45deg, #1b1f27, #3a4454);
  border-radius: 4px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.aspect-name {
  font-size: 13px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
