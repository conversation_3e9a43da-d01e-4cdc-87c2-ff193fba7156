<template>
  <div class="article-ai">
    <el-card
      class="article-ai-item1"
      :style="{ height: docHeight + 'px' }"
    >
      <h4 style="border-bottom: 1px solid #1A2029;padding-bottom: 20px;">AI内容生成</h4>

      <!-- 加载状态 -->
      <div
        v-if="loadingData"
        class="loading-container"
      >
        <div class="loader"></div>
        <p>加载表单配置中...</p>
      </div>

      <!-- 动态渲染表单字段 -->
      <div v-else>
        <div
          class="form-field"
          v-for="field in formData.forms"
          :key="field.name"
          :style="{order: field.sort}"
        >
          <h5>
            {{ field.name }}
            <span
              v-if="field.is_required === 1"
              class="required"
            >*</span>
          </h5>

          <div class="field-content">
            <!-- 文本输入 -->
            <el-input
              v-if="field.type === 5 && field.name !== '写作主题'"
              v-model="field.value[0]"
              style="width: 100%;"
              :placeholder="field.placeholder || `请输入${field.name}`"
              clearable
              :disabled="loading"
              type="textarea"
              autosize
            />

            <!-- 数字输入 -->
            <el-input
              v-if="field.type === 4"
              v-model.number="field.value[0]"
              style="width: 100%;"
              :placeholder="field.placeholder || `请输入${field.name}`"
              type="number"
              clearable
              :disabled="loading"
            />

            <!-- 单选 -->
            <el-radio-group
              v-if="field.type === 2"
              v-model="field.value[0]"
              :disabled="loading"
            >
              <el-radio
                v-for="option in field.options"
                :key="option"
                :label="option"
                class="radio-option"
              >
                {{ option }}
              </el-radio>
            </el-radio-group>

            <!-- 多选（改为标签输入模式） -->
            <div
              v-if="field.type === 3"
              class="tag-group"
            >
              <el-tag
                v-for="(tag, index) in field.value"
                :key="index"
                closable
                @close="field.value.splice(index,1)"
                class="checkbox-tag"
                type="success"
                effect="light"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-model="field._input"
                size="small"
                placeholder=" + Enter Add Tag"
                @keyup.enter="if(field._input && field.value.indexOf(field._input)===-1){field.value.push(field._input);} field._input='';"
                @blur="field._input='';"
                style="width: 110px;margin-bottom: 5px;"
              />
            </div>

            <!-- reasoning_intensity 开关 -->
            <el-switch
              v-if="field.type === 11"
              v-model="field.value[0]"
              :active-value="'enabled'"
              :inactive-value="'disabled'"
              :disabled="loading"
            />

            <!-- 文本域（带字符计数） -->
            <div v-if="field.type === 5 && field.name === '写作主题'">
              <textarea
                class="textarea"
                rows="8"
                :placeholder="field.placeholder || `请输入${field.name}`"
                v-model="field.value[0]"
                maxlength="800"
                :disabled="loading"
              ></textarea>
              <div
                class="char-count"
                :class="{ warning: field.value[0]?.length > 780 }"
              >{{ field.value[0]?.length || 0 }}/800</div>
            </div>
          </div>

          <!-- 字段说明 -->
          <p
            v-if="field.description"
            class="field-description"
          >{{ field.description }}</p>
          <p
            v-if="field.tips"
            class="field-tips"
          >{{ field.tips }}</p>
        </div>

        <!-- 按钮组：loading 状态生效 -->
        <div class="button-group">
          <button
            class="generate-button"
            @click="handleGenerate"
            :disabled="loading"
          >
            <span v-if="!loading">立即生成</span>
            <span
              v-else
              class="loader"
            ></span>
          </button>
          <button
            v-if="types !== 'doc'"
            class="polish-button"
            @click="handlePolish"
            :disabled="loadingPolish"
          >
            <span v-if="!loadingPolish">AI润色</span>
            <span
              v-else
              class="loader"
            ></span>
          </button>
        </div>
      </div>
    </el-card>
    <div
      class="article-ai-item2"
      :style="{ height: docHeight + 'px' }"
      style="padding: 20px;"
    >
      <Emptyed v-if="!aiContent && !loading" />
      <div
        :style="{ height: docHeight + 'px' }"
        class="ai-content-render"
        v-html="aiContent"
        v-else-if="aiContent || loading"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { generateArticle } from '@/apis/article';
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import Emptyed from '/Users/<USER>/Desktop/JC-project/ai-fronend/components/emptyed.vue';
import { useRoute } from 'vue-router';
import { getDocById } from '@/utils/dataLoader';
import { getScreenHeight } from '@/utils/mix-height';

const docHeight = ref(0);
onMounted(() => {
  const screenHeight = getScreenHeight();
  const headerHeight = 180; // 假设 header 高度为 60px，根据实际情况调整
  docHeight.value = screenHeight - headerHeight;
  console.log('屏幕高度:', screenHeight, '容器高度:', docHeight.value);
});

defineOptions({
  name: 'DynamicForm',
});

const route = useRoute();

// 表单数据 - 使用reactive以便深层响应式
const formData = reactive({
  id: 0,
  forms: [],
  intention: 1,
});
// 默认表单配置 - 当接口无返回时使用
const defaultFormConfig = {
  id: 0,
  forms: [
    {
      name: '需求',
      type: 5,
      sort: 1,
      description: '',
      tips: '',
      value: [''],
      is_required: 1,
      options: [''],
      placeholder: '请输入您的需求',
    },
    {
      name: '内容要点',
      type: 3,
      sort: 2,
      description: '',
      tips: '',
      value: [],
      is_required: 2,
      options: ['突出重点', '语言简洁', '生动有趣', '专业严谨'],
      placeholder: '',
    },
    {
      name: '深度思考',
      type: 11,
      sort: 100,
      description:
        '开启后影匠AI将引入Doubao Thinking进行深度思考，达到更优的创作效果，但可能增加等待时长。',
      tips: '',
      value: ['1'],
      is_required: 2,
      options: ['1', '2'],
      placeholder: '',
    },
  ],
  intention: 1,
};

const id = ref<string | null>(null);
const types = ref<string | null>(null);
const aiContent = ref(''); // 渲染AI生成内容
const loading = ref(false); // 生成加载状态
const loadingPolish = ref(false); // 润色加载状态
const loadingData = ref(true); // 数据加载状态

// 表单验证
function validateForm(formFields: any[]): boolean {
  const requiredFields = formFields.filter((field) => field.is_required === 1);

  for (const field of requiredFields) {
    // 检查必填字段是否有值
    if (
      !field.value ||
      field.value.length === 0 ||
      (Array.isArray(field.value) && field.value.every((v) => !v)) ||
      (typeof field.value === 'string' && !field.value.trim())
    ) {
      ElMessage.warning(`${field.name}为必填项`);
      return false;
    }
  }

  return true;
}

// 构建提示信息
function buildPrompt(formFields: any[]): string {
  let prompt = '';

  formFields.forEach((field) => {
    // 跳过 reasoning_intensity（type=11）的字段
    if (field.type === 11) return;

    if (field.value && field.value.length > 0) {
      const value = Array.isArray(field.value)
        ? field.value.join('，')
        : field.value;
      prompt += `${field.name}：${value}\n`;
    }
  });

  return prompt;
}
async function handleGenerate() {
  // 1. 校验输入 + 启用loading
  if (!validateForm(formData.forms)) {
    return;
  }

  loading.value = true; // 启用加载状态
  aiContent.value = ''; // 清空历史内容

  try {
    const prompt = buildPrompt(formData.forms);
    const response = await fetch(
      'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${import.meta.env.VITE_ARK_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'ep-20250910112858-wxh4p',
          messages: [
            {
              role: 'system',
              content: '你是一个专业的内容创作者，能根据用户需求生成高质量内容',
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          stream: true, // 确保流式开启
          thinking: {
            type:
              formData.forms.find((f: any) => f.type === 11)?.value[0] ||
              'auto',
          },
        }),
      },
    );

    // 2. 校验响应状态
    if (!response.ok) throw new Error(`请求失败：${response.status}`);
    const reader = response.body?.getReader();
    const decoder = new TextDecoder('utf-8');
    if (!reader) throw new Error('无法读取流式响应');

    let buffer = '';
    while (true) {
      const { done, value } = await reader.read();
      if (done) break; // 流结束，退出循环

      // 3. 解码数据 + 处理buffer
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 保留最后不完整的行（下次继续解析）

      // 4. 逐行解析
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue; // 跳过空行

        if (!trimmedLine.startsWith('data: ')) {
          console.warn('无效的流式数据格式', line);
          continue;
        }
        const dataContent = trimmedLine.slice(6);

        if (dataContent === '[DONE]') {
          console.log('流式数据已全部接收完成');
          continue;
        }

        // 5. 解析JSON并追加内容
        try {
          const data = JSON.parse(dataContent);
          if (data.choices?.[0]?.delta?.content) {
            aiContent.value += data.choices[0].delta.content;

            // 6. 自动滚动
            const container = document.querySelector('.ai-content-render');
            if (container) {
              requestAnimationFrame(() => {
                container.scrollTop = container.scrollHeight;
              });
            }
          }
        } catch (parseErr) {
          console.warn(
            '单条流式数据解析失败：',
            parseErr,
            '数据内容：',
            dataContent,
          );
          continue;
        }
      }
    }

    ElMessage.success('生成完成');
  } catch (err) {
    console.error('生成错误：', err);
    ElMessage.error('生成失败，请重试');
  } finally {
    loading.value = false;
  }
}

async function handlePolish() {
  // 1. 校验输入 + 启用loading
  if (!validateForm(formData.forms)) {
    return;
  }

  loadingPolish.value = true;

  try {
    const prompt = buildPrompt(formData.forms);
    const res = await generateArticle({
      model: 'ep-20250905170945-tz6xs',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的内容润色专家，能根据用户需求优化内容质量',
        },
        {
          role: 'user',
          content: `请帮我润色以下内容：${prompt}`,
        },
      ],
    });

    // 找到内容字段并更新
    const contentField = formData.forms.find(
      (f: any) => f.type === 5 && f.name.includes('主题'),
    );
    if (contentField) {
      contentField.value[0] = res.choices?.[0]?.message?.content || '';
    } else {
      // 如果没有主题字段，更新第一个文本输入字段
      const firstTextField = formData.forms.find((f: any) => f.type === 5);
      if (firstTextField) {
        firstTextField.value[0] = res.choices?.[0]?.message?.content || '';
      }
    }

    ElMessage.success('润色完成');
  } catch (err) {
    console.error('润色错误：', err);
    ElMessage.error('润色失败，请重试');
  } finally {
    loadingPolish.value = false;
  }
}

// 根据ID从接口获取文档数据并配置表单
async function fetchDocById() {
  if (!id.value) {
    console.warn('No id provided');
    // 使用默认配置
    Object.assign(formData, defaultFormConfig);
    loadingData.value = false;
    return;
  }

  try {
    loadingData.value = true;
    const res = await getDocById(id.value);
    console.log('Document fetched by id:', res);

    // 假设接口返回的数据结构与我们需要的表单结构一致
    if (res && Array.isArray(res.forms)) {
      Object.assign(formData, res); // 保留 id / intention
    } else {
      Object.assign(formData, defaultFormConfig);
      ElMessage.warning('表单配置数据格式不正确，使用默认配置');
    }
  } catch (error) {
    console.error('Error fetching document by id:', error);
    // 发生错误时使用默认配置
    Object.assign(formData, defaultFormConfig);
    ElMessage.error('加载表单配置失败，使用默认配置');
  } finally {
    loadingData.value = false;
  }
}

onMounted(() => {
  // 从路由参数中获取id
  id.value = route.query.id ? String(route.query.id) : null;
  types.value = route.query.type ? String(route.query.type) : null;
  // 触发接口请求获取表单配置
  fetchDocById();
});
</script>

<style lang="scss" scoped>
.ai-content-render {
  overflow-y: scroll;
  min-height: 600px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  line-height: 30px;
}
.ai-content-render::-webkit-scrollbar {
  display: none;
}

.article-ai {
  display: grid;
  grid-template-columns: 3fr 7fr;
  gap: 20px;
  width: 100%;
  height: 100%;
  background-color: #000;
  color: #fff;
  padding: 20px;
}

@media screen and (max-width: 1210px) {
  .article-ai {
    grid-template-columns: 1fr;
  }
}
.article-ai-item1 {
  overflow-y: auto;
  scrollbar-width: none;
  padding: 20px;
  &::-webkit-scrollbar {
    display: none;
  }
}

.article-ai-item2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.article-ai-item1,
.article-ai-item2 {
  background-color: #000;
  color: #fff;
  min-height: 600px;
  border: none;
  box-shadow: 0 0px 5px rgba(215, 213, 213, 0.6);
  transition: 0.3s all linear;
}

.article-ai-item1:hover,
.article-ai-item2:hover {
  box-shadow: 0 0px 12px rgba(215, 213, 213, 0.6);
}

.form-field {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.required {
  color: #ff6b6b;
  margin-left: 5px;
}

.field-content {
  margin-top: 10px;
}

.radio-option,
.checkbox-option {
  margin-right: 15px;
  margin-bottom: 10px;
}

.textarea {
  width: 100%;
  border-radius: 8px;
  background: linear-gradient(145deg, #1b1f27, #262d37);
  padding: 12px;
  color: #e0e0e0;
  border: 1px solid transparent;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.6;
  caret-color: #4ad3a5;
  transition: all 0.3s ease;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &:focus {
    border: 1px solid #4ad3a5;
    box-shadow: 0 0 12px rgba(74, 211, 165, 0.6);
    background: linear-gradient(145deg, #20242d, #2c3440);
  }
}

.textarea::-webkit-scrollbar {
  display: none;
}

.char-count {
  text-align: right;
  font-size: 12px;
  margin-top: 6px;
  color: #888;
  &.warning {
    color: #ff6b6b;
  }
}

.button-group {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 10px;
  margin-top: 20px;
}

.generate-button,
.polish-button {
  width: 100%;
  padding: 12px 20px;
  border-radius: 8px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.generate-button {
  background: linear-gradient(90deg, #4ad3a5, #2bb0ed);
  box-shadow: 0 0 10px rgba(74, 211, 165, 0.6);
  &:hover:not(:disabled) {
    background: linear-gradient(90deg, #2bb0ed, #4ad3a5);
    box-shadow: 0 0 20px rgba(74, 211, 165, 0.9);
    transform: translateY(-2px);
  }
}

.polish-button {
  background: linear-gradient(90deg, #f5a623, #f56a00);
  box-shadow: 0 0 10px rgba(245, 166, 35, 0.6);
  &:hover:not(:disabled) {
    background: linear-gradient(90deg, #f56a00, #f5a623);
    box-shadow: 0 0 20px rgba(245, 166, 35, 0.9);
    transform: translateY(-2px);
  }
}

.loader {
  display: inline-block;
  width: 17px;
  height: 17px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid #4ad3a5;
  border-right: 2px solid #2bb0ed;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.field-description {
  font-size: 12px;
  color: #888;
  margin-top: 5px;
}

.field-tips {
  font-size: 12px;
  color: #4ad3a5;
  margin-top: 5px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-container .loader {
  margin-bottom: 15px;
  width: 30px;
  height: 30px;
}

.el-textarea__inner {
  resize: none; /* 禁止拉伸 */
}

.checkbox-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}
</style>
