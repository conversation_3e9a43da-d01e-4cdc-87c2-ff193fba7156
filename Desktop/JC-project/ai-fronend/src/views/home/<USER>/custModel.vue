<template>
    <div class="temp-view" >
        <div class="title-wrapper">
            <h1 style="color: #fff;">{{ t('marketing.workshop.title') }}</h1>
            <h5 style="color: #CDCCCD;margin-top: 10px;">{{ t('marketing.workshop.subtitle') }}</h5>
        </div>
        <div class="card-wrapper">
            <div class="cust-card" v-for="item in tempList" :key="item.id">
                <video class="cust-video" type="video/mp4"
                autoplay
                muted
                playsinline
                loop :src="item.videoSrc"></video>
                <div class="cust-card-content">
                    <div class="cust-card-title">
                        <span>{{ item.name }}</span>
                        <span>{{ item.describe }}</span>
                    </div>
                    <div class="cust-card-btn" >
                        <div class="btn" >立即生成</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>
<script  lang="ts" setup>
defineOptions({ name: 'custModel' });
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();


const tempList = ref([
    { id: 1, name: '素材洞察', describe: '专业的素材诊断解决方案', videoSrc: 'https://media.vivago.ai/b9ed32c5-c8a6-45dc-85a6-c526da3ea44b.mp4' },
    { id: 2, name: 'TikTok广告脚本生成', describe: '-键生成TikTok推广视频脚本文案(不消耗︶积分)', videoSrc: 'https://media.vivago.ai/51c6856c-60ec-4a32-8855-0a8251ededfb.mp4' },
    { id: 3, name: 'TikTok风格数字人', describe: '键生成TikTok风格数字人(不消耗积分)', videoSrc: 'https://media.vivago.ai/f38d0dbb-55d6-4252-bc1e-cb0ca881f02f.mp4' },
    { id: 4, name: '视频翻译', describe: '一翻译音频和转换适配口型(不消耗积恃分)', videoSrc: 'https://media.vivago.ai/f6182198-b91d-4f45-afdf-dde82a86eb4d.mp4' },
    // { id: 5, name: '测试1', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/87cc69c1-125a-4d06-8ba9-224c7394aa5c.mp4' },
    // { id: 6, name: '测试2', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/6533dc3d-dab8-491b-84a6-1aa910815beb.mp4' },
    // { id: 7, name: '测试3', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/972264fb-4ea1-4490-b4e3-97db05d4ab2b.mp4' },
    // { id: 8, name: '测试4', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/63a34f7b-82b7-4660-a039-a9b5a15dfcce.mp4' },
    // { id: 9, name: '测试5', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/f9db992e-0518-436e-90f8-f43a8671da74.mp4' },
    // { id: 10, name: '测试6', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/170ddf90-3b5c-4080-a792-c3471afe0100.mp4' },
    // { id: 11, name: '测试7', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/b688ad58-26b6-4ebb-9d8c-814172773c3d.mp4' },
    // { id: 12, name: '测试8', describe: '测试测试测试测试测试测试测试', videoSrc: 'https://media.vivago.ai/ea84b243-d6e4-4486-98dd-e5e8b4328b19.mp4' },
])
</script>
<style lang="scss" scoped>
.temp-view{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    height: 100%;
    background-color: #000;
    margin-top: 30px;
}
.title-wrapper{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}
.card-wrapper{
   display: grid;
   grid-template-columns: repeat(4, 1fr);
   gap: 20px;
   width: 100%;
}
@media screen and (max-width: 1124px)  {
    .card-wrapper{
        grid-template-columns: repeat(2, 1fr);
    }
}
@media screen and (max-width: 768px) {
    .card-wrapper{
        grid-template-columns: repeat(1, 1fr);
    }
}
.cust-card{
    margin-top: 20px;
    border-radius: 10px;
    background-color: transparent;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    overflow: hidden;
    .cust-video{
        width: 100%;
        height: 100%;
        transition: all 0.3s ease;
    }

    .cust-card-content{
        background-color:#313131 ;
        width: 100%;
        padding: 10px;
        .cust-card-title{
            display: flex;
            flex-direction: column;
            >span:nth-child(1){
                font-size: 16px;
            }
            >span:nth-child(2){
                font-size: 12px;
                color: #CDCCCD;

            }

        }
        .cust-card-btn{
            width: 100%;
            margin-top: 30px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .btn{
                color: #fff;
                padding: 6px 16px;
                border-radius: 5px;
                background-color: #2A2F3E;
                font-size: 12px;
                transition: 0.3s all linear;
                cursor: pointer;
            }
        }
    }
}
.cust-card:hover{
    .cust-video{
        transform: scale(1.07);
        transition: all 0.3s linear;
    }
    .cust-card-btn{
        .btn{
            background-color: #4AD3A5;
            color: #fff;
            transition: 0.3s all linear;
        }
    }
}
</style>