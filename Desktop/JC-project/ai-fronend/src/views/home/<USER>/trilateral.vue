<template>

  <div
    class="trilateral"
    style="color: #fff;"
  >
    <div
      class="trilateral-item"
      v-for="item in list"
      :key="item.id"
    >
      <img
        style="width: 100%;height: 150px;border-radius: 5px;"
        :src="item.imgs"
        alt=""
      >
      <div style="display: flex; flex-direction:
            column;align-items: flex-start;border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            justify-content: flex-start;background-color: #313131;padding: 10px;">
        <p style="font-size: 13px;margin-top: 10px;">{{ item.name }}</p>
        <p style="font-size: 11px;color: #6D6D6D;margin-top: 10px;">{{ item.title    }}</p>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineOptions({
  name: 'Trilateral',
});
import { ref } from 'vue';
const list = ref([
  {
    id: 1,
    name: '小红书种草界的“最强AI大脑”，懂爆款、会拿捏，创意灵感不断，轻松日更十篇',
    title: '小红书助手',
    imgs: 'https://static5.yingsaidata.com/lrb/img/1531677-xhs-mlm.jpg?x-image-process=image/resize,p_100',
  },
  {
    id: 2,
    name: '基于文旅品牌的文化背景、历史故事和地域特色，生成独特的壁纸创意设计方案',
    imgs: 'https://static5.yingsaidata.com/99eedac6ffa096c6b65ba48c77663eb9.png?x-image-process=image/resize,p_100',
    title: '产品设计、文旅从业人员',
  },
  {
    id: 3,
    name: '美食品牌、餐饮企业或美食推广活动提供高质量的宣传视觉素材',
    title: '餐饮、美食、食品、食品包装',
    imgs: 'https://static5.yingsaidata.com/1a9cb6a6ce895df8f6bd862074cde321.png?x-image-process=image/resize,p_100',
  },
  {
    id: 4,
    name: '吸引潜在游客为目的的旅游设计，推广特定的旅游目的地、旅游线路或相关活动',
    title: '零售、营销策划、营销运营',
    imgs: 'https://static5.yingsaidata.com/e3b468367f838b19cec25e0e8cccf903.png?x-image-process=image/resize,p_100',
  },
]);
</script>
<style lang="scss" scoped>
.trilateral {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 两列 */
  gap: 20px; /* spacing between cards */
  height: 100%;
  width: 100%;
  margin-top: 15px;
  border-radius: 5px;
  .trilateral-item {
    height: 100%;
    background-color: #000;
    display: flex;
    flex-direction: column;
    border-radius: 5px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
  }
  .trilateral-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  }
}
@media screen and (max-width: 1124px) {
  .trilateral {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>