<template>
    <div>
        <ArticleAi/>
        <h4 style="color: #fff;margin-top: 30px;">品牌热门</h4>
        <trilateral/>
        <custModel/>
    </div>
</template>
<script lang="ts" setup>
    import { ref,reactive } from 'vue'
    import ArticleAi from './components/articleAi.vue'
    import custModel from './components/custModel.vue';
    import trilateral from './components/trilateral.vue';
</script>