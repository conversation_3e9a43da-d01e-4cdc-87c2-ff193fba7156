<template>
  <div class="article-ai">

    <el-card class="article-ai-item" @click="handleClick(item.id)" style="border: 1px solid;" v-for="item in articleList" :key="item.id">
      <img class="ability" :src="item.img" alt="">
      <p>{{ t(item.nameKey) }}</p>
      <p style="color: #8B99A8;font-size: 12px;">{{ t(item.descKey) }}</p>
      <div class="article-button">
        <span>{{ t(item.btnKey) }}</span>
      </div>
    </el-card>

  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
defineOptions({
  name: 'ArticleAi',
});

const { t } = useI18n();
const router = useRouter();

const articleList = ref([
  { id: 1, nameKey: 'feature.ai_article.title', descKey: 'feature.ai_article.desc', btnKey: 'button.generate_now', img:'/images/1.png' },
  { id: 2, nameKey: 'feature.ai_image.title', descKey: 'feature.ai_image.desc', btnKey: 'button.generate_now', img:'/images/2.png' },
  { id: 3, nameKey: 'feature.ai_video.title', descKey: 'feature.ai_video.desc', btnKey: 'button.generate_now', img:'/images/3.png'},
  { id: 4, nameKey: 'feature.digital_human.title', descKey: 'feature.digital_human.desc', btnKey: 'button.generate_now', img:'/images/4.png' },
]);
const handleClick = (id: number) => {
  switch (id) {
    case 1:
      router.push('/article');
      break;
    // case 2:
    //   router.push('/ai-image');
    //   break;
    // case 3:
    //   router.push('/ai-video');
    //   break;
    // case 4:
    //   router.push('/digital-human');
    //   break;
    // default:
    //   console.log('未知ID:', id);
  }
};
</script>
<style scoped>
.article-ai {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 两列 */
  gap: 20px; /* spacing between cards */
}

/* .article-ai > .el-card {
  flex: 1 1 calc(25% - 15px);
  box-sizing: border-box;
} */

.article-ai > .el-card p {
  font-size: 20px;
  font-style: oblique;
  font-weight: bold;
  margin-bottom: 8px;
  color: #B7D6EF;
  transition: 0.3s all linear;
}

@media screen and (max-width: 1124px)  {
  .article-ai {
     grid-template-columns: repeat(2, 1fr); /* 两列 */
  }
}

@media screen and (max-width: 768px) {
  .article-ai > .el-card {
    flex: 1 1 100%; /* full width, one column */
  }
  .article-ai {
     grid-template-columns: repeat(1, 1fr); /* 两列 */
  }
}
.article-ai-item {
  display: flex;
  flex-direction: column;
  background-color: #1E2533;
  padding-left: 20px;
  border:none;
  position: relative;

}
.ability{
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 100%;
    opacity: 0.3;
    transition: 0.3s all linear;
}
.article-ai-item:hover .ability {
  opacity: 1;
  transition: 0.3s all linear;
}
.article-ai-item:hover p{
    color: #fff;
    transition: 0.3s all linear;
}

.article-button {
  margin-top: auto;
  display: inline-block;
  padding: 6px 16px;
  border-radius: 6px;
  background-color: #2A2F3E;
  color: #B7D6EF;
  cursor: pointer;
  transition:0.3s all linear;
  font-size: 12px;
  margin-top: 10px;
  cursor: pointer;
}
.article-ai-item:hover .article-button {
  background-color: #4AD3A5;
  color: #fff;
  transition: 0.3s all linear;
  font-weight: bold;
}
</style>
