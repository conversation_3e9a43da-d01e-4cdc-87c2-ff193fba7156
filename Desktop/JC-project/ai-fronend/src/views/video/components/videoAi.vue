<template>
  <div class="video-ai">
    <el-card  class="video-ai-item1">
      <div class="video-ai-tabs">
        <div class="tab-item">
          <div
            class="tab-item-content"
            :class="{ 'active': activeIndex === item.id }"
            @click="handleClick(item.id)"
            v-for="item in modeList"
            :key="item.id"
          >{{ item.name }}</div>
        </div>
        <div style="margin-top: 20px;">
            <!-- 图片生视频 -->
          <div v-show="activeIndex === 1" style="display: flex;justify-content: center;align-items: center;width: 100%;">
            <el-upload
              style="width: 100%;"
              class="upload-demo"
              drag
              action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
              multiple
            >
              <div class="el-upload__text" style="display: flex;align-items: center;">
                <el-icon size="20px" style="margin-right: 10px;"><upload-filled /></el-icon>
                拖拽/<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  jpg/png files with a size less than 500kb
                </div>
              </template>
            </el-upload>
          </div>
            <!-- 文本生视频 -->
          <div v-show="activeIndex === 2" style="display: flex;justify-content: center;align-items: center;width: 100%;">
            <textarea class="textarea" rows="8"  placeholder="请输入文本"></textarea>
          </div>
        </div>
      </div>
    </el-card>
    <el-card class="video-ai-item2">

    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
defineOptions({
  name: 'VideoAi',
});
const activeIndex = ref(1);
function handleClick(id: number) {
  activeIndex.value = id;
}
const modeList = ref([
  {
    id: 1,
    name: '图片生视频',
  },
  {
    id: 2,
    name: '文本生视频',
  },
  {
    id: 3,
    name: '视频换脸',
  },
]);
</script>
<style lang="scss" scoped>
.video-ai {
  display: grid;
  grid-template-columns: 3fr 7fr;
  gap: 20px;
  width: 100%;
  height: 100%;
  background-color: #000;
}
@media screen and (max-width: 1124px) {
  .video-ai {
    grid-template-columns: 1fr;
  }
}
.video-ai-item1 {
  background-color: #000;
  color: #fff;
  .video-ai-tabs {
    background-color: #000;
    width: 100%;
    display: flex;
    flex-direction: column;
    .tab-item {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      width: 100%;
      height: 50px;
      line-height: 50px;
      background-color: #27303b;
      border-radius: 5px;
      .tab-item-content {
        font-size: 12px;
        color: #767474;
        cursor: pointer;
        width: 100%;
        text-align: center;
        transition: 0.3s all linear;
      }
      .active {
        color: #fff;
        transition: 0.3s all linear;
      }
    }
  }
}

.video-ai-item2 {
  background-color: #000;
  color: #fff;
  height: 100%;
}
:deep(.el-upload-dragger ){
    height: 100px;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.textarea{
    width: 100%;
    border-radius: 5px;
    background-color: #262D37;
    padding: 10px;
    color: #fff;
    border: none;
    resize: none;
    overflow: auto;                 /* enable scrolling */
    -ms-overflow-style: none;       /* IE 10+ */
    scrollbar-width: none;          /* Firefox */
}
.textarea::-webkit-scrollbar{
    width: 0;                        /* Chrome/Safari/WebKit */
    height: 0;
}
</style>