import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse, type InternalAxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

const http: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 5 * 60 * 1000,
})

http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 如果是 Ark API，就跳过自动加系统 token
    if (!config.url?.includes('ark.cn-beijing.volces.com')) {
      const token = getToken()
      if (token) {
        config.headers = config.headers || {}
        ;(config.headers as any).Authorization = `Bearer ${token}`
      }
    }
    return config
  },
  (error) => Promise.reject(error),
)

http.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    const message = error.response?.data?.msg || error.message || '服务器端错误'
    ElMessage.error(message)
    return Promise.reject(error)
  },
)

const request = async <T = any>(config: AxiosRequestConfig): Promise<T> => {
  const response = await http.request<T>(config)
  return response.data
}

const get = <T = any>(url: string, params?: object, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'get',
    url,
    params,
    ...config,
  })
}

const post = <T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'post',
    url,
    data,
    ...config,
  })
}

const put = <T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'put',
    url,
    data,
    ...config,
  })
}

const del = <T = any>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'delete',
    url,
    data,
    ...config,
  })
}

export default {
  request,
  get,
  post,
  put,
  delete: del,
}