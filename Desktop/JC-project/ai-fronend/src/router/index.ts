import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/home/<USER>'),
  },
  {
    path: '/explore',
    name: 'Explore',
    component: () => import('../views/explore/index.vue'),
  },
  {
    path: '/activity',
    name: 'Activity',
    component: () => import('../views/activity/index.vue'),
  },
  {
    path: '/picture',
    name: 'Picture',
    component: () => import('../views/picture/index.vue'),
  },
  {
    path: '/video',
    name: 'Video',
    component: () => import('../views/video/index.vue'),
  },
  {
    path: '/article',
    name: 'Article',
    component: () => import('../views/article/index.vue'),
  },
  {
    path: '/personal',
    name: 'Personal',
    component: () => import('../views/personal/index.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

export default router
