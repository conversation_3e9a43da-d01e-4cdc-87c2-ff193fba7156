import { createI18n } from 'vue-i18n';
import zhCn from './zh-cn';
import en from './en-us/index';
import type { LocaleMessages } from './types';

// 定义合法的语言类型
export type LocaleType = 'zh-cn' | 'en' ;

// 语言选择下拉框的选项
export const LOCALE_OPTIONS = [
  { label: '中文', value: 'zh-cn' },
  { label: 'English', value: 'en' },
];

const i18n = createI18n({
  locale: localStorage.getItem('locale') || 'zh-cn',
  fallbackLocale: 'zh-cn',
  legacy: false,
  allowComposition: true,
  messages: {
    'zh-cn': zhCn,
    'en': en,
  } as Record<LocaleType, LocaleMessages>
});

export default i18n;