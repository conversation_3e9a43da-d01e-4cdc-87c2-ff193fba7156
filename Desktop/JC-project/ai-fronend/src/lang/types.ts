/// 定义所有多语言文案的键名和值类型（约束所有语言包必须遵循此结构）
export interface LocaleMessages {
  // app 标题
  'app.title.main': string;       // 主标题
  'app.title.ai': string;         // AI副标题
  'app.language.zh': string;      // 中文选项
  'app.language.en': string;      // 英文选项

  // 左侧导航栏
  'nav.home': string;              // 首页导航
  'nav.explore': string;           // 探索导航
  'nav.activities': string;        // 活动导航
  'nav.ai_creation': string;       // AI 创作导航
  'nav.ai_picture': string;       // AI 图片创作
  'nav.ai_video': string;         // AI 视频创作
  'nav.personal_Center':string;   // 个人中心导航
  'nav.ai_article': string;  // 文章创作导航
  'nav.profile': string;           // 个人中心导航

  // 顶部功能区
  'feature.ai_article.title': string;
  'feature.ai_article.desc': string;
  'feature.ai_image.title': string;
  'feature.ai_image.desc': string;
  'feature.ai_video.title': string;
  'feature.ai_video.desc': string;
  'feature.digital_human.title': string;
  'feature.digital_human.desc': string;

  // 营销创意工坊区域
  'marketing.workshop.title': string;
  'marketing.workshop.subtitle': string;
  'marketing.insights.title': string;
  'marketing.insights.desc': string;
  'marketing.tiktok_script.title': string;
  'marketing.tiktok_script.desc': string;
  'marketing.tiktok_human.title': string;
  'marketing.tiktok_human.desc': string;
  'marketing.video_translate.title': string;
  'marketing.video_translate.desc': string;

  // 按钮文案
  'button.generate_now': string,
}