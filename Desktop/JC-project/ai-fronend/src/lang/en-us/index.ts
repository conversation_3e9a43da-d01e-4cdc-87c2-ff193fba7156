import type { LocaleMessages } from '../types';

// 英文文案：必须包含 LocaleMessages 接口的所有键名
const enMessages: LocaleMessages = {
  'app.title.main': 'Dream',
  'app.title.ai': 'AI',
  'app.language.zh': 'Chinese',
  'app.language.en': 'English',

  // 左侧导航栏
  'nav.home': 'Home',
  'nav.explore': 'Explore',
  'nav.activities': 'Activities',
  'nav.ai_creation': 'AI Creation',
  'nav.ai_picture': 'Picture',
  'nav.ai_video': 'Video',
  'nav.personal_Center': 'Center',
  'nav.ai_article': 'Article',
  'nav.profile': 'Profile',

  // 顶部功能区
  'feature.ai_article.title': 'AI Article',
  'feature.ai_article.desc': 'Enrich your inspirations, article generation',
  'feature.ai_image.title': 'AI Image',
  'feature.ai_image.desc': 'Easily create creative images, image generation',
  'feature.ai_video.title': 'AI Video',
  'feature.ai_video.desc': 'Bring your creativity to life, video generation',
  'feature.digital_human.title': 'Digital Human',
  'feature.digital_human.desc': 'Your exclusive AI creative assistant, avatar generation',

  // 营销创意工坊区域
  'marketing.workshop.title': 'Marketing Creative Workshop',
  'marketing.workshop.subtitle': 'Next-generation AI influencing new creative paradigms',
  'marketing.insights.title': 'Material Insights',
  'marketing.insights.desc': 'Professional material diagnostics, generate now',
  'marketing.tiktok_script.title': 'TikTok Ad Script Generation',
  'marketing.tiktok_script.desc': 'Generate TikTok promotional video scripts with one click (no points consumed), generate now',
  'marketing.tiktok_human.title': 'TikTok Style Digital Human',
  'marketing.tiktok_human.desc': 'Generate TikTok style digital human with one click (no points consumed), generate now',
  'marketing.video_translate.title': 'Video Translation',
  'marketing.video_translate.desc': 'Translate audio and sync dubbing with one click (no points consumed), generate now',

  // 按钮文案
  'button.generate_now': 'Generate Now',
};

export default enMessages;