{"code": 200, "data": [{"id": 0, "forms": [{"name": "需求", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["带货大美丽香水的清凉一号系列"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "产品特点", "type": 3, "sort": 2, "description": "", "tips": "", "value": ["全套仅需199", "国家发明专利清凉因子", "防蚊虫效果好", "母婴适用"], "is_required": 1, "options": ["全套仅需199", "国家发明专利清凉因子", "防蚊虫效果好", "母婴适用"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入DeepSeek-R1进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}], "intention": 1}, {"id": 1, "forms": [{"name": "视频主体", "type": 5, "sort": 1, "description": "请输入您的视频需要核心展示的主体。", "tips": "", "value": ["城市风光"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "视频要点", "type": 5, "sort": 2, "description": "需要展示的视频主体相关的多个核心要点。", "tips": "", "value": ["沿海；基础设施现代化；\nAI应用普遍；科研创新氛围浓厚；\n科研成果卓著；推动产教融合；\n人工智能陪伴成长；共创未来；"], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入DeepSeek-R1进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 2, "forms": [{"name": "种草品牌", "type": 5, "sort": 1, "description": "请输入您想要写哪方面的小红书文案？", "tips": "", "value": ["陆黎"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "种草产品", "type": 5, "sort": 2, "description": "", "tips": "", "value": ["新推出产品人参养生奶茶"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "品牌产品特点/理念", "type": 5, "sort": 3, "description": "", "tips": "", "value": ["元气满满，养足精气神，天然人参制好茶，20元买一送一，还送联名贴纸"], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "文案要求", "type": 3, "sort": 4, "description": "", "tips": "", "value": ["情感共鸣", "吸引用户", "制造紧迫感"], "is_required": 2, "options": ["情感共鸣", "吸引用户", "制造紧迫感"], "placeholder": ""}, {"name": "营销节点", "type": 3, "sort": 5, "description": "", "tips": "", "value": ["双十一购物节"], "is_required": 2, "options": ["双十一购物节"], "placeholder": ""}, {"name": "产品解决痛点", "type": 3, "sort": 6, "description": "", "tips": "", "value": ["会发胖", "不好吃", "糖分高", "口感差"], "is_required": 2, "options": ["会发胖", "不好吃", "糖分高", "口感差"], "placeholder": ""}, {"name": "用户群体", "type": 3, "sort": 7, "description": "", "tips": "", "value": ["大学生", "白领", "熬夜加班打工人"], "is_required": 2, "options": ["大学生", "白领", "熬夜加班打工人"], "placeholder": ""}, {"name": "备注", "type": 5, "sort": 8, "description": "", "tips": "", "value": [""], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "字数", "type": 4, "sort": 9, "description": "字数", "tips": "", "value": ["400"], "is_required": 2, "options": ["1", "5000"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入DeepSeek-R1进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 3, "forms": [{"name": "旅游行程", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["去巴黎3天行程"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "旅游关注点", "type": 3, "sort": 2, "description": "", "tips": "", "value": ["美食", "美景", "文化"], "is_required": 2, "options": ["美食", "美景", "文化"], "placeholder": ""}, {"name": "文章语言风格", "type": 2, "sort": 3, "description": "", "tips": "", "value": ["幽默有趣", "真实体验"], "is_required": 2, "options": ["幽默有趣", "真实体验"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入DeepSeek-R1进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 4, "forms": [{"name": "公众号类别", "type": 5, "sort": 1, "description": "什么类型的公众号", "tips": "", "value": ["电商"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "推广产品", "type": 5, "sort": 2, "description": "", "tips": "", "value": ["华为最新P60智能手机"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "产品特点", "type": 3, "sort": 3, "description": "", "tips": "", "value": ["强大的处理器", "高清晰摄像头", "长续航能力"], "is_required": 1, "options": ["强大的处理器", "高清晰摄像头", "长续航能力"], "placeholder": ""}, {"name": "营销策略", "type": 2, "sort": 4, "description": "", "tips": "", "value": ["限时折扣", "买赠活动"], "is_required": 2, "options": ["限时折扣", "买赠活动"], "placeholder": ""}, {"name": "购买引导", "type": 2, "sort": 5, "description": "", "tips": "", "value": ["购买链接", "强调库存有限", "二维码"], "is_required": 2, "options": ["购买链接", "强调库存有限", "二维码"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 5, "forms": [{"name": "教程主题", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["如何进行微信公众号运营"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 6, "forms": [{"name": "内容", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["可持续能源，是我们赖以生存的希望，是我们子孙后代的未来。当我想起这个话题，心中涌动着深深的感慨。\n我们生活在一个世界上，在每一刻都在不断地消耗着资源，无情地排放着污染。这是一个我们赖以生存的星球，但我们却不断地伤害它，让它在痛苦中挣扎。这不仅仅是对自然的伤害，更是对我们自身的残忍"], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "补充说明", "type": 5, "sort": 2, "description": "", "tips": "您可以补充任何对于续写的建议🤔", "value": [""], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 7, "forms": [{"name": "演讲稿", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["人工智能伦理"], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "主题理解", "type": 5, "sort": 2, "description": "", "tips": "", "value": ["人工智能伦理的相关概念和当前的挑战"], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "结构设计", "type": 2, "sort": 3, "description": "", "tips": "", "value": ["开头", "中间主题", "结尾"], "is_required": 2, "options": ["开头", "中间主题", "结尾"], "placeholder": ""}, {"name": " 观点明确", "type": 2, "sort": 4, "description": "", "tips": "", "value": ["观点", "立场"], "is_required": 2, "options": ["观点", "立场"], "placeholder": ""}, {"name": "语言魅力", "type": 3, "sort": 5, "description": "", "tips": "", "value": ["简洁", "生动", "有力"], "is_required": 2, "options": ["简洁", "生动", "有力"], "placeholder": ""}, {"name": "互动环节", "type": 2, "sort": 6, "description": "", "tips": "", "value": ["问答", "讨论"], "is_required": 2, "options": ["问答", "讨论"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 8, "forms": [{"name": "灵感需求", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["一篇关于修仙的小说，男主无法突破金丹期，需要一个机遇"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "灵感元素", "type": 3, "sort": 2, "description": "", "tips": "", "value": ["现实事件", "历史故事", "个人经历", "梦境", "自然景观"], "is_required": 2, "options": ["现实事件", "历史故事", "个人经历", "梦境", "自然景观"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 9, "forms": [{"name": "岗位", "type": 3, "sort": 1, "description": "", "tips": "", "value": ["产品经理"], "is_required": 2, "options": ["产品经理"], "placeholder": ""}, {"name": " 职位介绍", "type": 2, "sort": 2, "description": "", "tips": "", "value": ["产品规划", "需求分析", "项目管理"], "is_required": 2, "options": ["产品规划", "需求分析", "项目管理"], "placeholder": ""}, {"name": "公司介绍", "type": 2, "sort": 3, "description": "", "tips": "", "value": ["背景", "文化", "产品", "团队情况"], "is_required": 2, "options": ["背景", "文化", "产品", "团队情况"], "placeholder": ""}, {"name": "福利待遇", "type": 2, "sort": 4, "description": "", "tips": "", "value": ["薪资范围", "福利政策", "职业发展机会"], "is_required": 2, "options": ["薪资范围", "福利政策", "职业发展机会"], "placeholder": ""}, {"name": "应聘方式", "type": 2, "sort": 5, "description": "", "tips": "", "value": ["简历投递邮箱", "截止日期", "面试流程"], "is_required": 2, "options": ["简历投递邮箱", "截止日期", "面试流程"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 10, "forms": [{"name": "vlog主题", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["梅里雪山的旅游"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "主题设定", "type": 3, "sort": 2, "description": "", "tips": "", "value": ["旅行", "美食", "日常生活"], "is_required": 1, "options": ["旅行", "美食", "日常生活"], "placeholder": ""}, {"name": "故事叙述", "type": 2, "sort": 3, "description": "", "tips": "", "value": ["吸引人", "生动", "有趣"], "is_required": 1, "options": ["吸引人", "生动", "有趣"], "placeholder": ""}, {"name": "情感连接", "type": 2, "sort": 4, "description": "", "tips": "", "value": ["个人经验", "感受"], "is_required": 1, "options": ["个人经验", "感受"], "placeholder": ""}, {"name": "互动引导", "type": 2, "sort": 5, "description": "", "tips": "", "value": ["提问", "留言", "分享"], "is_required": 1, "options": ["提问", "留言", "分享"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 11, "forms": [{"name": "活动主题", "type": 5, "sort": 1, "description": "", "tips": "", "value": ["\"关爱老人，温暖社会\""], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "关键要素", "type": 3, "sort": 3, "description": "", "tips": "", "value": ["活动主题", "情感共鸣", "易于传播"], "is_required": 2, "options": ["活动主题", "情感共鸣", "易于传播"], "placeholder": ""}, {"name": "应用场景", "type": 5, "sort": 3, "description": "", "tips": "", "value": ["关爱老人公益活动"], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "实施建议", "type": 2, "sort": 4, "description": "", "tips": "", "value": ["从活动主题出发", "结合情感共鸣和易于传播的特点", "为活动设计一个富有内涵和吸引力的名称", "以提高活动的知名度和影响力"], "is_required": 2, "options": ["从活动主题出发", "结合情感共鸣和易于传播的特点", "为活动设计一个富有内涵和吸引力的名称", "以提高活动的知名度和影响力"], "placeholder": ""}, {"name": "深度思考", "type": 11, "sort": 100, "description": "开启后影匠AI将引入Doubao Thinking进行联合思考，达到更优的创作效果，但可能增加等待时长。", "tips": "", "value": ["1"], "is_required": 2, "options": ["1", "2"], "placeholder": ""}]}, {"id": 12, "forms": [{"name": "节日选择", "type": 5, "sort": 1, "description": "输入您想要的节日", "tips": "300字以内", "value": ["端午节"], "is_required": 1, "options": [""], "placeholder": ""}, {"name": "节日元素", "type": 3, "sort": 2, "description": "", "tips": "", "value": ["龙舟", "粽子", "艾草"], "is_required": 2, "options": ["龙舟", "粽子", "艾草"], "placeholder": ""}, {"name": "补充说明", "type": 5, "sort": 3, "description": "输入您对于海报确定其他补充信息🤔", "tips": "", "value": [""], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "色彩方案", "type": 3, "sort": 4, "description": "", "tips": "", "value": ["绿色", "红色"], "is_required": 2, "options": ["绿色", "红色"], "placeholder": ""}, {"name": "海报风格", "type": 6, "sort": 5, "description": "", "tips": "", "value": ["3"], "is_required": 2, "options": ["现实主义", "印象派", "卡通", "抽象", "超现实主义", "赛博朋克", "古风", "极简主义", "三维模型"], "placeholder": ""}, {"name": "海报尺寸", "type": 7, "sort": 6, "description": "", "tips": "", "value": ["9:16"], "is_required": 1, "options": ["9:16", "16:9", "1:1", "4:3", "3:4", "3:2", "2:3"], "placeholder": ""}, {"name": "垫图", "type": 8, "sort": 7, "description": "", "tips": "", "value": [""], "is_required": 2, "options": [""], "placeholder": ""}, {"name": "图片负面词", "type": 9, "sort": 8, "description": "", "tips": "", "value": [""], "is_required": 2, "options": [""], "placeholder": "请输入您不想在生成的内容出现的元素 \\n\n 比如：额外手指，画质模糊，低质量，噪点，曝光不足的，出现人物..."}, {"name": "海报布局控制", "type": 10, "sort": 9, "description": "", "tips": "", "value": ["2"], "is_required": 2, "options": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], "placeholder": ""}]}]}