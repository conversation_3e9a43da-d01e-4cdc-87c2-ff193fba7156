import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'  // 引入element-plus
import 'element-plus/dist/index.css'   // 引入element-plus样式
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import i18n from './lang';

const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(ElementPlus).use(i18n).use(i18n)
app.use(ElementPlus)
app.use(createPinia())
app.use(router)

app.mount('#app')
