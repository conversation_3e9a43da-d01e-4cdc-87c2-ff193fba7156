<template>
  <!-- 添加el-config-provider以支持Element Plus组件国际化 -->
  <el-config-provider :locale="elementLocale">
    <el-container style="height: 100vh;">
      <!-- 顶部导航 -->
      <el-header
        height="60px"
        class="header"
      >
        <div class="hearder-content">
          <!-- 移动端菜单按钮 -->

          <!-- 使用$t()实现标题国际化 -->
          <h2 style="color: #fff;">{{ $t('app.title.main') }} <span style="color: #A7BEFF;font-style: oblique;">{{ $t('app.title.ai') }}</span></h2>
          <div>
            <el-select
              v-model="currentLocale"
              @change="handleLocaleChange"
              size="small"
              style="width: 80px; "
            >
              <el-option
                :label="$t('app.language.zh')"
                value="zh-cn"
              />
              <el-option
                :label="$t('app.language.en')"
                value="en"
              />
            </el-select>
            <el-button
              v-if="isMobile"
              icon="Menu"
              @click="showMenu = !showMenu"
              type="text"
              size="small"
              style="margin-right: 10px;"
            />
          </div>

        </div>
      </el-header>

      <!-- 主体部分 -->
      <el-container>
        <!-- pc侧边栏 -->
        <el-aside
          v-show="!isMobile"
          style="width: 150px;"
        >
          <AppMenu :t="$t" />
        </el-aside>

        <!-- 移动端侧边栏 -->
        <el-drawer
          v-model="showMenu"
          direction="rtl"
          :with-header="false"
          :modal="true"
          size="180px"
          v-if="isMobile"
          :show-close="true"
          class="custom-drawer"
          :z-index="1000"
          modal-class="custom-drawer-overlay"
        >
          <AppMenu
            :t="$t"
            style="margin-top: 60px;"
          />
        </el-drawer>

        <!-- 内容区 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </el-config-provider>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import AppMenu from '../components/appMenu.vue';

// 导入Element Plus的语言包
import zhCn from 'element-plus/es/locale/lang/zh-cn.mjs';
import en from 'element-plus/es/locale/lang/en.mjs';

// 初始化i18n
const { locale } = useI18n();

// 设备响应式相关
const isMobile = ref(false);
const showMenu = ref(false);

// 国际化相关
const currentLocale = ref<string>(
  localStorage.getItem('locale') || 'zh-cn', // 从本地存储获取，默认中文
);

// 同步i18n的locale值
locale.value = currentLocale.value;

// 根据当前语言设置Element Plus的组件语言
const elementLocale = computed(() => {
  switch (currentLocale.value) {
    case 'en':
      return en;
    default:
      return zhCn;
  }
});

// 处理语言切换
const handleLocaleChange = (val: string) => {
  currentLocale.value = val;
  locale.value = val;
  localStorage.setItem('locale', val); // 保存到本地存储
};

// 设备尺寸检测
const updateDevice = () => {
  const width = window.innerWidth;
  isMobile.value = width < 768;
  if (isMobile.value) {
    showMenu.value = false;
  } else {
    showMenu.value = true;
  }
};

onMounted(() => {
  updateDevice();
  window.addEventListener('resize', updateDevice);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateDevice);
});
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

.header {
  background-color: #000;
  display: flex;
  align-items: center;
  padding-left: 20px;
  width: 100%;
  z-index: 2002;
  border-bottom: 1px solid #2e3136;
}

.hearder-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.el-divider__text {
  color: #7a7b7b;
  background-color: #000;
  font-size: 11px;
  padding: 0 10px;
}

.header {
  background-color: #000;
  display: flex;
  align-items: center;
  padding-left: 20px;
  width: 100%;
  z-index: 2002; /* 高于 drawer 的默认值 */
  border-bottom: 1px solid #2e3136;
}

.aside {
  background-color: #f2f2f2;
}

.main {
  background-color: #000;
  padding: 20px;
}

.hearder-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.custom-drawer .el-drawer__body {
  padding: 0;
  overflow: hidden;
}

/* Custom drawer overlay glass blur effect */
.custom-drawer-overlay {
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
}
.el-select--small .el-select__wrapper{
  background-color: #000;
}
.el-select__placeholder{
  color: #fff;
}
</style>
